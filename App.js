import 'react-native-gesture-handler';
import { StatusBar } from 'expo-status-bar';
import { useContext, useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './src/redux/store';
import AppNavigator from './src/navigation/AppNavigator';
import Toast from 'react-native-toast-message';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { ThemeProvider, ThemeContext } from './src/context/ThemeContext';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import VideoSplashScreen from "./src/screens/VideoSplashScreen";
import { AppStateManager } from './src/components/AppStateManager';
import notificationService from './src/services/notificationService';

function AppContent() {
  const { isDarkMode } = useContext(ThemeContext);

  // Initialize app services when AppContent mounts
  useEffect(() => {
    const initializeServices = async () => {
      // Initialize Push Notifications
      try {
        console.log('🔔 Initializing push notifications...');
        const pushToken = await notificationService.initialize();

        if (pushToken) {
          console.log('✅ Push notifications ready! Check console above for token and cURL command.');
        } else {
          console.log('⚠️ Push notifications not available (simulator or permissions denied)');
        }
      } catch (error) {
        console.error('❌ Failed to initialize push notifications:', error);
      }
    };

    initializeServices();

    // Cleanup function
    return () => {
      notificationService.cleanup();
    };
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <AppStateManager />
      <StatusBar
        style={isDarkMode ? 'light' : 'dark'}
        backgroundColor={isDarkMode ? '#1a1a1a' : '#ffffff'}
        translucent={false}
      />
      <SafeAreaProvider>
        <NavigationContainer>
          <SafeAreaView style={{
            flex: 1,
            backgroundColor: isDarkMode ? '#121212' : '#f5f5f5'
          }}>
            <AppNavigator />
            <Toast />
          </SafeAreaView>
        </NavigationContainer>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

export default function App() {
  const [splashFinished, setSplashFinished] = useState(false);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider>
          {!splashFinished ? (
            <>
              <StatusBar
                style="light"
                backgroundColor="#000000"
                translucent={false}
              />
              <VideoSplashScreen onFinish={() => setSplashFinished(true)} />
            </>
          ) : (
            <AppContent />
          )}
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
}
